// lib/core/theme/config/theme_config.dart

import 'package:flutter/material.dart';
import '../constants/constants_color.dart';
import '../constants/typography_constants.dart';
import '../constants/sizing_constants.dart';

class ThemeConfig {
  static ThemeData get lightTheme => _getTheme(Brightness.light);
  static ThemeData get darkTheme => _getTheme(Brightness.dark);

  static ThemeData _getTheme(Brightness brightness) {
    final baseTextTheme = brightness == Brightness.light
        ? ThemeData.light().textTheme
        : ThemeData.dark().textTheme;

    return ThemeData(
      useMaterial3: true,
      brightness: brightness,
      colorScheme: _getColorScheme(brightness),
      textTheme: _getTextTheme(baseTextTheme),
      elevatedButtonTheme: _elevatedButtonTheme(brightness),
      textButtonTheme: _textButtonTheme(brightness),
      outlinedButtonTheme: _outlinedButtonTheme(brightness),
      floatingActionButtonTheme: _floatingActionButtonTheme(brightness),
      appBarTheme: _appBarTheme(brightness),
      cardTheme: _cardTheme(brightness),
      inputDecorationTheme: _inputDecorationTheme(brightness),
      dialogTheme: _dialogTheme(brightness),
      bottomSheetTheme: _bottomSheetTheme(brightness),
      snackBarTheme: _snackBarTheme(brightness),
      chipTheme: _chipTheme(brightness),
      fontFamily: AppTypography.primaryFontFamily,
    );
  }

  static TextTheme _getTextTheme(TextTheme baseTheme) {
    return TextTheme(
      // Display styles
      displayLarge: baseTheme.displayLargeCustom,
      displayMedium: baseTheme.displayMediumCustom,
      displaySmall: baseTheme.displaySmallCustom,

      // Headline styles
      headlineLarge: baseTheme.headlineLargeCustom,
      headlineMedium: baseTheme.headlineMediumCustom,
      headlineSmall: baseTheme.headlineSmallCustom,

      // Title styles
      titleLarge: baseTheme.titleLargeCustom,
      titleMedium: baseTheme.titleMediumCustom,
      titleSmall: baseTheme.titleSmallCustom,

      // Body styles
      bodyLarge: baseTheme.bodyLargeCustom,
      bodyMedium: baseTheme.bodyMediumCustom,
      bodySmall: baseTheme.bodySmallCustom,

      // Label styles
      labelLarge: baseTheme.labelLargeCustom,
      labelMedium: baseTheme.labelMediumCustom,
      labelSmall: baseTheme.labelSmallCustom,
    );
  }

  static ColorScheme _getColorScheme(Brightness brightness) {
    return ColorScheme(
      brightness: brightness,
      primary: brightness == Brightness.light
          ? AppColors.primaryLight
          : AppColors.primaryDark,
      onPrimary: brightness == Brightness.light
          ? AppColors.buttonForegroundLight
          : AppColors.buttonForegroundDark,
      secondary: brightness == Brightness.light
          ? AppColors.secondaryLight
          : AppColors.secondaryDark,
      onSecondary: brightness == Brightness.light
          ? AppColors.buttonForegroundLight
          : AppColors.buttonForegroundDark,
      error: brightness == Brightness.light
          ? AppColors.incorrectLight
          : AppColors.incorrectDark,
      onError: brightness == Brightness.light
          ? AppColors.buttonForegroundLight
          : AppColors.buttonForegroundDark,
      surface: brightness == Brightness.light
          ? AppColors.backgroundLight
          : AppColors.backgroundDark,
      onSurface: brightness == Brightness.light
          ? AppColors.textLight
          : AppColors.textDark,
    );
  }

  static ElevatedButtonThemeData _elevatedButtonTheme(Brightness brightness) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizing.spaceL,
          vertical: AppSizing.spaceM,
        ),
        backgroundColor: brightness == Brightness.light
            ? AppColors.buttonBackgroundLight
            : AppColors.buttonBackgroundDark,
        foregroundColor: brightness == Brightness.light
            ? AppColors.buttonForegroundLight
            : AppColors.buttonForegroundDark,
        // shape: RoundedRectangleBorder(
        //   borderRadius: BorderRadius.circular(AppSizing.radiusM),
        // ),
        minimumSize:
            const Size(AppSizing.buttonWidthMin, AppSizing.buttonHeightM),
        maximumSize:
            const Size(AppSizing.buttonWidthMax, AppSizing.buttonHeightL),
        textStyle: const TextStyle(
          fontFamily: AppTypography.primaryFontFamily,
          fontWeight: AppTypography.medium,
        ),
      ),
    );
  }

  static TextButtonThemeData _textButtonTheme(Brightness brightness) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizing.spaceM,
          vertical: AppSizing.spaceS,
        ),
        foregroundColor: brightness == Brightness.light
            ? AppColors.primaryLight
            : AppColors.primaryDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizing.radiusS),
        ),
        minimumSize:
            const Size(AppSizing.buttonWidthMin, AppSizing.buttonHeightS),
        textStyle: const TextStyle(
          fontFamily: AppTypography.primaryFontFamily,
          fontWeight: AppTypography.medium,
        ),
      ),
    );
  }

  static OutlinedButtonThemeData _outlinedButtonTheme(Brightness brightness) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizing.spaceL,
          vertical: AppSizing.spaceM,
        ),
        foregroundColor: brightness == Brightness.light
            ? AppColors.primaryLight
            : AppColors.primaryDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizing.radiusM),
        ),
        side: BorderSide(
          color: brightness == Brightness.light
              ? AppColors.primaryLight
              : AppColors.primaryDark,
          width: 1.5,
        ),
        minimumSize:
            const Size(AppSizing.buttonWidthMin, AppSizing.buttonHeightM),
        textStyle: const TextStyle(
          fontFamily: AppTypography.primaryFontFamily,
          fontWeight: AppTypography.medium,
        ),
      ),
    );
  }

  static FloatingActionButtonThemeData _floatingActionButtonTheme(
      Brightness brightness) {
    return FloatingActionButtonThemeData(
      elevation: 4,
      backgroundColor: AppColors.buttonFAB,
      foregroundColor: brightness == Brightness.light
          ? AppColors.buttonForegroundLight
          : AppColors.buttonForegroundDark,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusFull),
      ),
    );
  }

  static AppBarTheme _appBarTheme(Brightness brightness) {
    return AppBarTheme(
      elevation: 0,
      centerTitle: true,
      scrolledUnderElevation: 2,
      backgroundColor: brightness == Brightness.light
          ? AppColors.backgroundLight
          : AppColors.backgroundDark,
      iconTheme: IconThemeData(
        color: brightness == Brightness.light
            ? AppColors.textLight
            : AppColors.textDark,
        size: AppSizing.iconM,
      ),
      titleTextStyle: TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontWeight: AppTypography.semiBold,
        fontSize: 20,
        color: brightness == Brightness.light
            ? AppColors.textLight
            : AppColors.textDark,
      ),
    );
  }

  static CardThemeData _cardTheme(Brightness brightness) {
    return CardThemeData(
      elevation: 2,
      color: brightness == Brightness.light
          ? AppColors.cardBackgroundLight
          : AppColors.cardBackgroundLight,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusL),
      ),
      margin: const EdgeInsets.all(AppSizing.spaceS),
    );
  }

  static InputDecorationTheme _inputDecorationTheme(Brightness brightness) {
    return InputDecorationTheme(
      filled: true,
      fillColor: (brightness == Brightness.light
              ? AppColors.backgroundLight
              : AppColors.backgroundDark)
          .withAlpha(204), // 0.8 * 255 = 204
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppSizing.spaceM,
        vertical: AppSizing.spaceS,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        borderSide: BorderSide(
          color: (brightness == Brightness.light
                  ? AppColors.primaryLight
                  : AppColors.primaryDark)
              .withAlpha(128), // 0.5 * 255 = 128
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        borderSide: BorderSide(
          color: (brightness == Brightness.light
                  ? AppColors.primaryLight
                  : AppColors.primaryDark)
              .withAlpha(77), // 0.3 * 255 = 77
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        borderSide: BorderSide(
          color: brightness == Brightness.light
              ? AppColors.primaryLight
              : AppColors.primaryDark,
          width: 2,
        ),
      ),
      labelStyle: TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontWeight: AppTypography.regular,
        color: brightness == Brightness.light
            ? AppColors.textLight
            : AppColors.textDark,
      ),
      hintStyle: TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontWeight: AppTypography.regular,
        color: brightness == Brightness.light
            ? AppColors.textLight.withValues(alpha: 0.6)
            : AppColors.textDark.withValues(alpha: 0.6),
      ),
    );
  }

  static DialogThemeData _dialogTheme(Brightness brightness) {
    return DialogThemeData(
      elevation: 8,
      backgroundColor: brightness == Brightness.light
          ? AppColors.backgroundLight
          : AppColors.backgroundDark,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusL),
      ),
      titleTextStyle: TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: 20,
        fontWeight: AppTypography.semiBold,
        color: brightness == Brightness.light
            ? AppColors.textLight
            : AppColors.textDark,
      ),
      contentTextStyle: TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: 16,
        fontWeight: AppTypography.regular,
        color: brightness == Brightness.light
            ? AppColors.textLight
            : AppColors.textDark,
      ),
    );
  }

  static BottomSheetThemeData _bottomSheetTheme(Brightness brightness) {
    return BottomSheetThemeData(
      elevation: 8,
      backgroundColor: brightness == Brightness.light
          ? AppColors.backgroundLight
          : AppColors.backgroundDark,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
    );
  }

  static SnackBarThemeData _snackBarTheme(Brightness brightness) {
    return SnackBarThemeData(
      behavior: SnackBarBehavior.fixed,
      backgroundColor: brightness == Brightness.light
          ? AppColors.infoLight
          : AppColors.infoDark,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
      ),
      contentTextStyle: TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodyMedium,
        fontWeight: AppTypography.regular,
        color: brightness == Brightness.light
            ? AppColors.buttonForegroundLight
            : AppColors.buttonForegroundDark,
      ),
    );
  }

  static ChipThemeData _chipTheme(Brightness brightness) {
    return ChipThemeData(
      backgroundColor: brightness == Brightness.light
          ? AppColors.secondaryLight.withValues(alpha: 0.2)
          : AppColors.secondaryDark.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizing.radiusFull),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizing.spaceM,
        vertical: AppSizing.spaceXS,
      ),
      labelStyle: TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.labelLarge,
        fontWeight: AppTypography.medium,
        color: brightness == Brightness.light
            ? AppColors.textLight
            : AppColors.textDark,
      ),
    );
  }
}

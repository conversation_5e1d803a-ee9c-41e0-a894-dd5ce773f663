import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/features/notifications/services/admin_notification_service.dart';
import 'package:vocadex/src/features/notifications/services/local_notification_service.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';

class SendNotificationCard extends ConsumerStatefulWidget {
  const SendNotificationCard({super.key});

  @override
  ConsumerState<SendNotificationCard> createState() =>
      _SendNotificationCardState();
}

class _SendNotificationCardState extends ConsumerState<SendNotificationCard> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _bodyController = TextEditingController();
  final _imageUrlController = TextEditingController();
  bool _isLoading = false;
  bool _isScheduled = false;
  DateTime? _scheduledDate;
  TimeOfDay? _scheduledTime;

  @override
  void dispose() {
    _titleController.dispose();
    _bodyController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _sendNotification() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate scheduling if scheduled
    if (_isScheduled) {
      if (_scheduledDate == null || _scheduledTime == null) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Please select both date and time for scheduling.',
        );
        return;
      }

      final scheduledDateTime = DateTime(
        _scheduledDate!.year,
        _scheduledDate!.month,
        _scheduledDate!.day,
        _scheduledTime!.hour,
        _scheduledTime!.minute,
      );

      if (scheduledDateTime.isBefore(DateTime.now())) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Scheduled time must be in the future.',
        );
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final adminService = ref.read(adminNotificationServiceProvider);
      final authState = ref.read(authStateNotifierProvider);

      String? createdBy;
      authState.whenOrNull(
        authenticated: (user) => createdBy = user.email,
      );

      if (_isScheduled) {
        final scheduledDateTime = DateTime(
          _scheduledDate!.year,
          _scheduledDate!.month,
          _scheduledDate!.day,
          _scheduledTime!.hour,
          _scheduledTime!.minute,
        );

        await adminService.scheduleNotificationToAll(
          title: _titleController.text.trim(),
          body: _bodyController.text.trim(),
          scheduledAt: scheduledDateTime,
          imageUrl: _imageUrlController.text.trim().isEmpty
              ? null
              : _imageUrlController.text.trim(),
          createdBy: createdBy,
        );
      } else {
        await adminService.sendPushNotificationToAll(
          title: _titleController.text.trim(),
          body: _bodyController.text.trim(),
          imageUrl: _imageUrlController.text.trim().isEmpty
              ? null
              : _imageUrlController.text.trim(),
          createdBy: createdBy,
        );
      }

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: _isScheduled
              ? 'Notification scheduled successfully!'
              : 'Notification sent successfully!',
        );

        // Clear form
        _titleController.clear();
        _bodyController.clear();
        _imageUrlController.clear();
        setState(() {
          _isScheduled = false;
          _scheduledDate = null;
          _scheduledTime = null;
        });
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Failed to send notification: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testLocalNotification() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final localService = ref.read(localNotificationServiceProvider);

      // Check permissions first
      final permissionGranted = await localService.requestPermission();
      if (!permissionGranted) {
        throw Exception('Notification permission not granted');
      }

      // Check if notifications are enabled
      final enabled = await localService.areNotificationsEnabled();
      if (!enabled) {
        throw Exception('Notifications are disabled in device settings');
      }

      // Create test notification
      final testNotification = NotificationModel(
        id: 'test_local_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Local Notification Test',
        body:
            'This is a test local notification. If you see this, local notifications are working!',
        type: NotificationType.local,
        data: {'test': true},
      );

      // Show immediate local notification
      await localService.showNotification(testNotification);

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Test Sent',
          description:
              'Test local notification sent! Check your notification panel.',
        );
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Test Failed',
          description: 'Local notification test failed: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testTriggerScheduling() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final localService = ref.read(localNotificationServiceProvider);

      // Create a test trigger scheduled for 2 minutes from now
      final now = DateTime.now();
      final testTime = now.add(const Duration(minutes: 2));

      // Format time for trigger schedule
      final timeString =
          '${testTime.hour.toString().padLeft(2, '0')}:${testTime.minute.toString().padLeft(2, '0')}';

      final testTrigger = TriggerEvent(
        id: 'test_trigger_${now.millisecondsSinceEpoch}',
        name: 'Test Trigger',
        description: 'Test trigger for immediate testing',
        type: TriggerType.dailyReminder,
        enabled: true,
        title: 'Trigger Test! 🧪',
        message:
            'This is a test trigger notification. Your trigger system is working!',
        schedule: TriggerSchedule(
          type: ScheduleType.daily,
          time: timeString,
        ),
        createdAt: now,
      );

      // Schedule the test trigger
      await localService.setupTriggerEvents([testTrigger]);

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Test Trigger Scheduled',
          description:
              'Test trigger scheduled for ${testTime.hour}:${testTime.minute.toString().padLeft(2, '0')}. You should receive a notification in 2 minutes!',
        );
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Test Failed',
          description: 'Trigger test failed: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _scheduledDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _scheduledDate) {
      setState(() {
        _scheduledDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _scheduledTime ?? TimeOfDay.now(),
    );
    if (picked != null && picked != _scheduledTime) {
      setState(() {
        _scheduledTime = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(
                    Icons.send,
                    color: Colors.blue,
                    size: 24,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Send Push Notification',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Title field
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Notification Title',
                  hintText: 'Enter notification title',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  if (value.trim().length > 50) {
                    return 'Title must be 50 characters or less';
                  }
                  return null;
                },
                maxLength: 50,
              ),
              const SizedBox(height: 16),

              // Body field
              TextFormField(
                controller: _bodyController,
                decoration: const InputDecoration(
                  labelText: 'Notification Message',
                  hintText: 'Enter notification message',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.message),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a message';
                  }
                  if (value.trim().length > 200) {
                    return 'Message must be 200 characters or less';
                  }
                  return null;
                },
                maxLength: 200,
              ),
              const SizedBox(height: 16),

              // Image URL field (optional)
              TextFormField(
                controller: _imageUrlController,
                decoration: const InputDecoration(
                  labelText: 'Image URL (Optional)',
                  hintText: 'Enter image URL',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.image),
                ),
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final uri = Uri.tryParse(value.trim());
                    if (uri == null || !uri.hasScheme) {
                      return 'Please enter a valid URL';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Scheduling options
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('Send Now'),
                      value: false,
                      groupValue: _isScheduled,
                      onChanged: (value) {
                        setState(() {
                          _isScheduled = value!;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('Schedule'),
                      value: true,
                      groupValue: _isScheduled,
                      onChanged: (value) {
                        setState(() {
                          _isScheduled = value!;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),

              // Scheduling fields (shown only when scheduled)
              if (_isScheduled) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: _selectDate,
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Date',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _scheduledDate != null
                                ? '${_scheduledDate!.day}/${_scheduledDate!.month}/${_scheduledDate!.year}'
                                : 'Select date',
                            style: TextStyle(
                              color: _scheduledDate != null
                                  ? null
                                  : Theme.of(context).hintColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: _selectTime,
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Time',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.access_time),
                          ),
                          child: Text(
                            _scheduledTime != null
                                ? _scheduledTime!.format(context)
                                : 'Select time',
                            style: TextStyle(
                              color: _scheduledTime != null
                                  ? null
                                  : Theme.of(context).hintColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],

              const SizedBox(height: 8),

              // Send button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _sendNotification,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          _isScheduled
                              ? 'Schedule Notification'
                              : 'Send to All Users',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),

              const SizedBox(height: 12),

              // Test Local Notification Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: _isLoading ? null : _testLocalNotification,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.orange,
                    side: const BorderSide(color: Colors.orange),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Test Local Notification',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Test Trigger Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: _isLoading ? null : _testTriggerScheduling,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.purple,
                    side: const BorderSide(color: Colors.purple),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Test Trigger (2 min delay)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Info text
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _isScheduled
                            ? 'This will schedule the notification for delivery at the specified time.'
                            : 'This will send the notification to all app users immediately.',
                        style: const TextStyle(
                          color: Colors.blue,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

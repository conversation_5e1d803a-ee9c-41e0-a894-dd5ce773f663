import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';
import 'package:vocadex/src/features/notifications/utils/trigger_event_factory.dart';
import 'package:uuid/uuid.dart';

part 'admin_notification_service.g.dart';

@riverpod
AdminNotificationService adminNotificationService(Ref ref) {
  return AdminNotificationService();
}

class AdminNotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Uuid _uuid = const Uuid();

  /// Send push notification to all users immediately
  Future<void> sendPushNotificationToAll({
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? data,
    String? createdBy,
  }) async {
    try {
      final notification = NotificationModel(
        id: _uuid.v4(),
        title: title,
        body: body,
        type: NotificationType.push,
        status: NotificationStatus.pending,
        imageUrl: imageUrl,
        data: data,
        createdAt: DateTime.now(),
        createdBy: createdBy,
      );

      // Save notification to Firestore
      await _firestore
          .collection('notifications')
          .doc(notification.id)
          .set(notification.toFirestore());

      // Mark as sent (in a real implementation, this would be done by Cloud Functions)
      await _firestore.collection('notifications').doc(notification.id).update({
        'status': NotificationStatus.sent.name,
        'sentAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Push notification sent: ${notification.id}');
    } catch (e) {
      debugPrint('Error sending push notification: $e');
      rethrow;
    }
  }

  /// Schedule push notification to all users for later delivery
  Future<void> scheduleNotificationToAll({
    required String title,
    required String body,
    required DateTime scheduledAt,
    String? imageUrl,
    Map<String, dynamic>? data,
    String? createdBy,
  }) async {
    try {
      final notification = NotificationModel(
        id: _uuid.v4(),
        title: title,
        body: body,
        type: NotificationType.push,
        status: NotificationStatus.pending,
        imageUrl: imageUrl,
        data: data,
        scheduledAt: scheduledAt,
        createdAt: DateTime.now(),
        createdBy: createdBy,
      );

      // Save scheduled notification to Firestore
      await _firestore
          .collection('notifications')
          .doc(notification.id)
          .set(notification.toFirestore());

      debugPrint(
          'Push notification scheduled: ${notification.id} for $scheduledAt');
    } catch (e) {
      debugPrint('Error scheduling push notification: $e');
      rethrow;
    }
  }

  /// Get all notifications (for admin history)
  Future<List<NotificationModel>> getNotificationHistory() async {
    try {
      final snapshot = await _firestore
          .collection('notifications')
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error fetching notification history: $e');
      return [];
    }
  }

  /// Stream of notification history
  Stream<List<NotificationModel>> watchNotificationHistory() {
    return _firestore
        .collection('notifications')
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => NotificationModel.fromFirestore(doc.data(), doc.id))
            .toList());
  }

  /// Create or update trigger event
  Future<void> saveTriggerEvent(TriggerEvent trigger) async {
    try {
      await _firestore
          .collection('trigger_events')
          .doc(trigger.id)
          .set(trigger.toFirestore());

      debugPrint('Trigger event saved: ${trigger.id}');
    } catch (e) {
      debugPrint('Error saving trigger event: $e');
      rethrow;
    }
  }

  /// Get all trigger events
  Future<List<TriggerEvent>> getTriggerEvents() async {
    try {
      final snapshot = await _firestore
          .collection('trigger_events')
          .orderBy('createdAt', descending: false)
          .get();

      return snapshot.docs
          .map((doc) => TriggerEvent.fromFirestore(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error fetching trigger events: $e');
      return [];
    }
  }

  /// Stream of trigger events
  Stream<List<TriggerEvent>> watchTriggerEvents() {
    return _firestore
        .collection('trigger_events')
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => TriggerEvent.fromFirestore(doc.data(), doc.id))
            .toList());
  }

  /// Delete trigger event
  Future<void> deleteTriggerEvent(String triggerId) async {
    try {
      await _firestore.collection('trigger_events').doc(triggerId).delete();

      debugPrint('Trigger event deleted: $triggerId');
    } catch (e) {
      debugPrint('Error deleting trigger event: $e');
      rethrow;
    }
  }

  /// Create default trigger events (called during setup)
  Future<void> createDefaultTriggerEvents() async {
    try {
      final defaultTriggers = TriggerEventFactory.createAllDefaultTriggers();

      for (final trigger in defaultTriggers) {
        await saveTriggerEvent(trigger);
      }

      debugPrint('Created ${defaultTriggers.length} default trigger events');
    } catch (e) {
      debugPrint('Error creating default trigger events: $e');
      rethrow;
    }
  }
}
